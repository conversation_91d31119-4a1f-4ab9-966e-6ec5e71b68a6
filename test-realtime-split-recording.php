<?php

require_once __DIR__ . '/vendor/autoload.php';

use LiveStream\LiveStream;
use LiveStream\PlatformFactory;
use LiveStream\Recording\PendingRecorder;
use LiveStream\Config\RecordingOptions;
use LiveStream\Recording\Advanced\PhpFFmpegRecorder;
use LiveStream\Enum\Quality;
use LiveStream\Enum\OutputFormat;

echo "🔴 实时分割录制测试\n";
echo "====================\n";
echo "特点：录制过程中直接分割，支持无限录制和动态停止\n\n";

try {
    // 1. 创建直播平台驱动
    $liveStream = new LiveStream(new PlatformFactory());
    $platform = $liveStream->driver('https://live.douyin.com/426219276305');

    echo "✅ 成功连接到直播间\n";

    // 2. 获取房间信息
    $roomInfo = $platform->getRoomInfo();
    echo "房间ID: " . $roomInfo->getRoomId() . "\n";
    echo "主播: " . $roomInfo->getAnchorName() . "\n";
    echo "标题: " . $roomInfo->getTitle() . "\n";
    echo "是否在线: " . ($roomInfo->isLive() ? '是' : '否') . "\n\n";

    if (!$roomInfo->isLive()) {
        echo "❌ 直播间当前未开播，无法录制\n";
        exit(1);
    }

    // 3. 创建实时分割录制选项
    echo "📋 分割策略选择:\n";
    echo "1. 时间分割 (每30秒)\n";
    echo "2. 大小分割 (每50MB)\n";
    echo "3. 混合分割 (每20秒或30MB)\n";
    echo "4. 无限录制 (仅在主播下播时停止)\n\n";

    // 这里我们演示混合分割策略
    $options = new RecordingOptions(
        quality: Quality::HIGH,
        format: OutputFormat::MP4,
        savePath: './realtime-recordings',
        splitTime: 15,        // 每15秒分割
        maxFileSize: 20,      // 或达到20MB时分割
        timeoutSeconds: 120   // 最多录制2分钟（演示用）
    );

    // 4. 创建录制配置
    $pendingRecorder = new PendingRecorder($roomInfo, $options);

    echo "🎯 实时分割录制配置:\n";
    $recordingInfo = $pendingRecorder->getRecordingInfo();
    echo "  录制ID: " . $recordingInfo['record_id'] . "\n";
    echo "  分割策略: 混合分割 (每{$options->splitTime}秒或{$options->maxFileSize}MB)\n";
    echo "  最大录制时间: {$options->timeoutSeconds} 秒\n";
    echo "  输出目录: " . dirname($recordingInfo['output_path']) . "\n";
    echo "  流地址: " . substr($recordingInfo['stream_url'], 0, 80) . "...\n\n";

    // 5. 创建录制器
    $recorder = new PhpFFmpegRecorder();

    echo "🔴 开始实时分割录制...\n";
    echo "📡 实时监控直播状态，主播下播时自动停止\n";
    echo "⏱️  每{$options->splitTime}秒或{$options->maxFileSize}MB自动分割\n";
    echo "🛑 按 Ctrl+C 可提前停止录制\n\n";

    // 6. 设置信号处理（优雅停止）
    if (function_exists('pcntl_signal')) {
        pcntl_signal(SIGINT, function () {
            echo "\n🛑 收到停止信号，正在优雅停止录制...\n";
        });
    }

    // 7. 执行实时分割录制
    $startTime = time();
    $result = $recorder->execute($pendingRecorder, function ($media, $format, $percentage, $segment = null) {
        if ($segment) {
            echo "\r📹 分段 {$segment->index} 录制进度: " . number_format($percentage, 1) . "% ";

            // 显示实时文件大小
            if (file_exists($segment->outputPath)) {
                $currentSize = filesize($segment->outputPath) / 1024 / 1024;
                echo "(当前: " . number_format($currentSize, 1) . "MB)";
            }
        } else {
            echo "\r📹 录制进度: " . number_format($percentage, 1) . "%";
        }

        // 处理信号
        if (function_exists('pcntl_signal_dispatch')) {
            pcntl_signal_dispatch();
        }
    });

    $totalTime = time() - $startTime;

    echo "\n\n✅ 实时分割录制完成！\n";
    echo "总录制时间: {$totalTime} 秒\n";

    // 8. 检查生成的文件
    echo "\n📁 检查生成的分段文件...\n";
    $recordingDir = './realtime-recordings';

    if (is_dir($recordingDir)) {
        $files = glob($recordingDir . '/*.mp4');
        sort($files);

        $totalSize = 0;
        $totalDuration = 0;

        echo "生成的文件列表:\n";
        foreach ($files as $index => $file) {
            $fileSize = filesize($file);
            $fileSizeMB = $fileSize / 1024 / 1024;
            $totalSize += $fileSize;

            echo sprintf("  %d. %s (%.2f MB)\n", $index + 1, basename($file), $fileSizeMB);

            // 获取视频时长
            $videoInfo = shell_exec("ffprobe -v quiet -print_format json -show_format " . escapeshellarg($file));
            if ($videoInfo) {
                $info = json_decode($videoInfo, true);
                if (isset($info['format']['duration'])) {
                    $duration = (float)$info['format']['duration'];
                    $totalDuration += $duration;
                    echo "     时长: " . number_format($duration, 1) . " 秒\n";
                }
            }
        }

        $totalSizeMB = $totalSize / 1024 / 1024;
        echo "\n📊 统计信息:\n";
        echo "  文件总数: " . count($files) . " 个\n";
        echo "  总大小: " . number_format($totalSizeMB, 2) . " MB\n";
        echo "  总时长: " . number_format($totalDuration, 1) . " 秒\n";
        echo "  平均分段大小: " . number_format($totalSizeMB / count($files), 2) . " MB\n";
        echo "  平均分段时长: " . number_format($totalDuration / count($files), 1) . " 秒\n";

        echo "\n🎯 实时分割的优势:\n";
        echo "  ✅ 录制过程中直接分割，无需后处理\n";
        echo "  ✅ 支持无限录制，主播下播时自动停止\n";
        echo "  ✅ 内存友好，不需要存储完整视频\n";
        echo "  ✅ 支持多种分割策略（时间、大小、混合）\n";
        echo "  ✅ 网络中断后可以恢复录制\n";
        echo "  ✅ 实时监控直播状态和文件大小\n";
    } else {
        echo "❌ 录制目录不存在或未生成文件\n";
    }
} catch (Exception $e) {
    echo "\n❌ 实时分割录制失败: " . $e->getMessage() . "\n";
    echo "错误类型: " . get_class($e) . "\n";

    if (method_exists($e, 'getContext')) {
        echo "错误上下文: " . json_encode($e->getContext(), JSON_PRETTY_PRINT) . "\n";
    }
}

echo "\n=== 实时分割录制测试结束 ===\n";
