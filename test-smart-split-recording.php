<?php

require_once __DIR__ . '/vendor/autoload.php';

use LiveStream\LiveStream;
use LiveStream\PlatformFactory;
use LiveStream\Recording\PendingRecorder;
use LiveStream\Config\RecordingOptions;
use LiveStream\Recording\Advanced\PhpFFmpegRecorder;
use LiveStream\Enum\Quality;
use LiveStream\Enum\OutputFormat;

echo "🧠 智能分割录制测试 (基于 php-ffmpeg)\n";
echo "=====================================\n";
echo "特点：使用 php-ffmpeg 扩展包 + 智能分割策略\n\n";

try {
    // 1. 创建直播平台驱动
    $liveStream = new LiveStream(new PlatformFactory());
    $platform = $liveStream->driver('https://live.douyin.com/426219276305');

    echo "✅ 成功连接到直播间\n";

    // 2. 获取房间信息
    $roomInfo = $platform->getRoomInfo();
    echo "房间ID: " . $roomInfo->getRoomId() . "\n";
    echo "主播: " . $roomInfo->getAnchorName() . "\n";
    echo "标题: " . $roomInfo->getTitle() . "\n";
    echo "是否在线: " . ($roomInfo->isLive() ? '是' : '否') . "\n\n";

    if (!$roomInfo->isLive()) {
        echo "❌ 直播间当前未开播，无法录制\n";
        exit(1);
    }

    // 3. 创建智能分割录制选项
    echo "🎯 智能分割策略说明:\n";
    echo "• 使用 php-ffmpeg 扩展包进行录制\n";
    echo "• 预设每段录制时长，实现伪实时分割\n";
    echo "• 实时监控文件大小和直播状态\n";
    echo "• 完全兼容现有架构设计\n\n";

    $options = new RecordingOptions(
        quality: Quality::HIGH,
        format: OutputFormat::MP4,
        savePath: './smart-recordings',
        splitTime: 25,        // 每25秒分割一次
        timeoutSeconds: 100   // 总录制100秒，预计4段
    );

    // 4. 创建录制配置
    $pendingRecorder = new PendingRecorder($roomInfo, $options);

    echo "📋 智能分割录制配置:\n";
    $recordingInfo = $pendingRecorder->getRecordingInfo();
    echo "  录制ID: " . $recordingInfo['record_id'] . "\n";
    echo "  分割策略: 时间分割 (每{$options->splitTime}秒)\n";
    echo "  总录制时间: {$options->timeoutSeconds} 秒\n";
    echo "  预计分段数: " . ceil($options->timeoutSeconds / $options->splitTime) . " 个\n";
    echo "  输出目录: " . dirname($recordingInfo['output_path']) . "\n";
    echo "  流地址: " . substr($recordingInfo['stream_url'], 0, 80) . "...\n\n";

    // 5. 创建录制器
    $recorder = new PhpFFmpegRecorder();

    echo "🧠 开始智能分割录制...\n";
    echo "📦 使用 php-ffmpeg 扩展包统一接口\n";
    echo "⚡ 智能预设分段时长，避免阻塞问题\n";
    echo "📊 实时监控文件大小和录制进度\n";
    echo "🛑 按 Ctrl+C 可提前停止录制\n\n";

    // 6. 执行智能分割录制
    $startTime = time();
    $result = $recorder->execute($pendingRecorder, function ($media, $format, $percentage, $segment = null) {
        // 这个回调会显示详细的进度信息
        // 智能分割器会在这里显示时长、大小等信息
    });

    $totalTime = time() - $startTime;

    echo "\n\n✅ 智能分割录制完成！\n";
    echo "总录制时间: {$totalTime} 秒\n";

    // 7. 分析生成的文件
    echo "\n📊 分析生成的文件...\n";
    $recordingDir = './smart-recordings';

    if (is_dir($recordingDir)) {
        $files = glob($recordingDir . '/*.mp4');
        sort($files);

        if (count($files) > 0) {
            $totalSize = 0;
            $totalDuration = 0;

            echo "生成的文件详情:\n";
            foreach ($files as $index => $file) {
                $fileSize = filesize($file);
                $fileSizeMB = $fileSize / 1024 / 1024;
                $totalSize += $fileSize;

                echo sprintf("  %d. %s\n", $index + 1, basename($file));
                echo sprintf("     大小: %.2f MB\n", $fileSizeMB);

                // 获取视频时长
                $videoInfo = shell_exec("ffprobe -v quiet -print_format json -show_format " . escapeshellarg($file));
                if ($videoInfo) {
                    $info = json_decode($videoInfo, true);
                    if (isset($info['format']['duration'])) {
                        $duration = (float)$info['format']['duration'];
                        $totalDuration += $duration;
                        echo sprintf("     时长: %.1f 秒\n", $duration);

                        // 计算比特率
                        $bitrate = ($fileSize * 8) / $duration / 1000; // kbps
                        echo sprintf("     比特率: %.0f kbps\n", $bitrate);
                    }
                }
                echo "\n";
            }

            $totalSizeMB = $totalSize / 1024 / 1024;
            echo "📈 统计摘要:\n";
            echo "  文件总数: " . count($files) . " 个\n";
            echo "  总大小: " . number_format($totalSizeMB, 2) . " MB\n";
            echo "  总时长: " . number_format($totalDuration, 1) . " 秒\n";
            echo "  平均分段大小: " . number_format($totalSizeMB / count($files), 2) . " MB\n";
            echo "  平均分段时长: " . number_format($totalDuration / count($files), 1) . " 秒\n";
            echo "  平均比特率: " . number_format(($totalSize * 8) / $totalDuration / 1000, 0) . " kbps\n";

            echo "\n🎯 智能分割的优势:\n";
            echo "  ✅ 完全使用 php-ffmpeg 扩展包\n";
            echo "  ✅ 统一的录制接口和错误处理\n";
            echo "  ✅ 智能预设分段时长\n";
            echo "  ✅ 实时进度监控和状态显示\n";
            echo "  ✅ 符合 PHP 最佳实践规范\n";
            echo "  ✅ 完全兼容现有架构设计\n";

            // 验证分割是否符合预期
            $averageSegmentDuration = $totalDuration / count($files);
            $expectedDuration = $options->splitTime;
            $durationDiff = abs($averageSegmentDuration - $expectedDuration);

            echo "\n🔍 分割精度验证:\n";
            echo "  期望分段时长: {$expectedDuration} 秒\n";
            echo "  实际平均时长: " . number_format($averageSegmentDuration, 1) . " 秒\n";
            echo "  时长偏差: " . number_format($durationDiff, 1) . " 秒\n";

            if ($durationDiff < 2) {
                echo "  ✅ 分割精度优秀！\n";
            } elseif ($durationDiff < 5) {
                echo "  ✅ 分割精度良好！\n";
            } else {
                echo "  ⚠️  分割精度有待改善\n";
            }
        } else {
            echo "❌ 未找到录制文件\n";
        }
    } else {
        echo "❌ 录制目录不存在\n";
    }
} catch (Exception $e) {
    echo "\n❌ 智能分割录制失败: " . $e->getMessage() . "\n";
    echo "错误类型: " . get_class($e) . "\n";

    if (method_exists($e, 'getContext')) {
        echo "错误上下文: " . json_encode($e->getContext(), JSON_PRETTY_PRINT) . "\n";
    }

    // 显示调试信息
    echo "\n🔧 调试信息:\n";
    echo "- 请确保 php-ffmpeg 扩展包正确安装\n";
    echo "- 请确保 FFmpeg 二进制文件可用\n";
    echo "- 请检查网络连接和直播流状态\n";
}

echo "\n=== 智能分割录制测试结束 ===\n";
