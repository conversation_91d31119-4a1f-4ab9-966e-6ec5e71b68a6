<?php

require_once __DIR__ . '/vendor/autoload.php';

use LiveStream\LiveStream;
use LiveStream\PlatformFactory;
use LiveStream\Recording\PendingRecorder;
use LiveStream\Config\RecordingOptions;
use LiveStream\Recording\Advanced\PhpFFmpegRecorder;
use LiveStream\Enum\Quality;
use LiveStream\Enum\OutputFormat;

echo "🎥 抖音直播录制测试\n";
echo "===================\n\n";

try {
    // 1. 创建直播平台驱动
    $liveStream = new LiveStream(new PlatformFactory());
    $platform = $liveStream->driver('https://live.douyin.com/426219276305');

    echo "✅ 成功连接到直播间\n";

    // 2. 获取房间信息
    $roomInfo = $platform->getRoomInfo();
    echo "房间ID: " . $roomInfo->getRoomId() . "\n";
    echo "主播: " . $roomInfo->getAnchorName() . "\n";
    echo "标题: " . $roomInfo->getTitle() . "\n";
    echo "是否在线: " . ($roomInfo->isLive() ? '是' : '否') . "\n\n";

    if (!$roomInfo->isLive()) {
        echo "❌ 直播间当前未开播，无法录制\n";
        exit(1);
    }

    // 3. 创建录制选项
    $options = new RecordingOptions(
        quality: Quality::HIGH,
        format: OutputFormat::MP4,
        savePath: './test-recordings'
    );

    // 4. 创建录制配置
    $pendingRecorder = new PendingRecorder($roomInfo, $options);

    echo "📋 录制配置:\n";
    $recordingInfo = $pendingRecorder->getRecordingInfo();
    echo "  录制ID: " . $recordingInfo['record_id'] . "\n";
    echo "  画质: " . $recordingInfo['quality'] . "\n";
    echo "  格式: " . $recordingInfo['format'] . "\n";
    echo "  输出路径: " . $recordingInfo['output_path'] . "\n";
    echo "  流地址: " . substr($recordingInfo['stream_url'], 0, 80) . "...\n\n";

    // 5. 创建录制器
    $recorder = new PhpFFmpegRecorder();

    echo "🎬 开始录制（按 Ctrl+C 停止）...\n";
    echo "输出文件: " . $pendingRecorder->getOutputPath() . "\n\n";

    // 6. 执行录制（带进度回调）
    $startTime = time();
    $progressCallback = function ($media, $format, $percentage) use ($startTime) {
        $elapsed = time() - $startTime;
        echo sprintf("\r录制中... %d%% (已录制 %d 秒)", $percentage, $elapsed);
    };

    $result = $recorder->execute($pendingRecorder, $progressCallback);

    echo "\n\n✅ 录制完成！\n";
    echo "输出文件: " . $result->getOutputPath() . "\n";

    // 7. 检查文件
    if (file_exists($result->getOutputPath())) {
        $fileSize = filesize($result->getOutputPath());
        echo "文件大小: " . number_format($fileSize / 1024 / 1024, 2) . " MB\n";

        // 使用 FFmpeg 获取视频信息
        $videoInfo = shell_exec("ffprobe -v quiet -print_format json -show_format -show_streams " . escapeshellarg($result->getOutputPath()));
        if ($videoInfo) {
            $info = json_decode($videoInfo, true);
            if (isset($info['format']['duration'])) {
                echo "视频时长: " . number_format($info['format']['duration'], 2) . " 秒\n";
            }
        }
    } else {
        echo "❌ 录制文件未找到\n";
    }
} catch (Exception $e) {
    echo "\n❌ 录制失败: " . $e->getMessage() . "\n";
    echo "错误类型: " . get_class($e) . "\n";

    if (method_exists($e, 'getContext')) {
        echo "错误上下文: " . json_encode($e->getContext(), JSON_PRETTY_PRINT) . "\n";
    }
}

echo "\n=== 录制测试结束 ===\n";
