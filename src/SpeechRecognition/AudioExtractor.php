<?php

declare(strict_types=1);

namespace LiveStream\SpeechRecognition;

use FFMpeg\FFMpeg;
use FFMpeg\Format\Audio\Wav;
use FFMpeg\Format\Audio\Mp3;
use FFMpeg\Coordinate\TimeCode;
use LiveStream\Exceptions\AudioExtractionException;
use Psr\Log\LoggerInterface;
use Psr\Log\NullLogger;

/**
 * 音频提取器
 * 
 * 从视频文件中提取音频，优化用于语音识别
 */
class AudioExtractor
{
    private FFMpeg $ffmpeg;
    private LoggerInterface $logger;
    
    public function __construct(?FFMpeg $ffmpeg = null, ?LoggerInterface $logger = null)
    {
        $this->ffmpeg = $ffmpeg ?? FFMpeg::create([
            'ffmpeg.binaries'  => '/usr/bin/ffmpeg',
            'ffprobe.binaries' => '/usr/bin/ffprobe',
            'timeout'          => 3600, // 1小时超时
            'ffmpeg.threads'   => 12,   // 多线程处理
        ]);
        
        $this->logger = $logger ?? new NullLogger();
    }
    
    /**
     * 从视频文件提取音频（优化用于语音识别）
     */
    public function extractAudio(
        string $videoPath, 
        string $outputPath, 
        AudioExtractionOptions $options = null
    ): AudioExtractionResult {
        $options = $options ?? new AudioExtractionOptions();
        
        try {
            $this->logger->info('开始音频提取', [
                'video_path' => $videoPath,
                'output_path' => $outputPath,
                'options' => $options->toArray()
            ]);
            
            $startTime = microtime(true);
            
            // 验证输入文件
            $this->validateInputFile($videoPath);
            
            // 打开视频文件
            $video = $this->ffmpeg->open($videoPath);
            
            // 配置音频格式
            $format = $this->createAudioFormat($options);
            
            // 提取音频
            if ($options->hasTimeRange()) {
                // 提取指定时间段
                $video = $video->clip(
                    TimeCode::fromSeconds($options->getStartTime()),
                    TimeCode::fromSeconds($options->getDuration())
                );
            }
            
            // 保存音频文件
            $video->save($format, $outputPath);
            
            $processingTime = microtime(true) - $startTime;
            
            // 获取音频信息
            $audioInfo = $this->getAudioInfo($outputPath);
            
            $result = new AudioExtractionResult(
                outputPath: $outputPath,
                duration: $audioInfo['duration'],
                sampleRate: $audioInfo['sample_rate'],
                channels: $audioInfo['channels'],
                fileSize: filesize($outputPath),
                processingTime: $processingTime
            );
            
            $this->logger->info('音频提取完成', [
                'result' => $result->toArray(),
                'processing_time' => $processingTime
            ]);
            
            return $result;
            
        } catch (\Exception $e) {
            $this->logger->error('音频提取失败', [
                'video_path' => $videoPath,
                'error' => $e->getMessage()
            ]);
            
            throw new AudioExtractionException(
                "音频提取失败: {$e->getMessage()}",
                0,
                $e
            );
        }
    }
    
    /**
     * 批量提取音频（用于分段录制文件）
     */
    public function extractAudioBatch(array $videoPaths, string $outputDir, AudioExtractionOptions $options = null): array
    {
        $results = [];
        $options = $options ?? new AudioExtractionOptions();
        
        foreach ($videoPaths as $videoPath) {
            $filename = pathinfo($videoPath, PATHINFO_FILENAME);
            $outputPath = $outputDir . '/' . $filename . '.wav';
            
            try {
                $results[$videoPath] = $this->extractAudio($videoPath, $outputPath, $options);
            } catch (AudioExtractionException $e) {
                $this->logger->error('批量提取中的文件失败', [
                    'video_path' => $videoPath,
                    'error' => $e->getMessage()
                ]);
                
                $results[$videoPath] = null;
            }
        }
        
        return $results;
    }
    
    /**
     * 创建优化的音频格式
     */
    private function createAudioFormat(AudioExtractionOptions $options): Wav|Mp3
    {
        if ($options->getFormat() === 'mp3') {
            $format = new Mp3();
            $format->setAudioKiloBitrate($options->getBitrate());
        } else {
            $format = new Wav();
        }
        
        $format->setAudioChannels($options->getChannels())
               ->setAudioSampleRate($options->getSampleRate());
        
        // 语音识别优化参数
        $format->setAdditionalParameters([
            '-af', 'highpass=f=80,lowpass=f=8000', // 语音频率范围滤波
            '-ar', (string)$options->getSampleRate(), // 采样率
            '-ac', (string)$options->getChannels(),   // 声道数
        ]);
        
        return $format;
    }
    
    /**
     * 获取音频文件信息
     */
    private function getAudioInfo(string $audioPath): array
    {
        $audio = $this->ffmpeg->open($audioPath);
        $streams = $audio->getStreams();
        
        $audioStream = $streams->audios()->first();
        
        return [
            'duration' => (float)$audio->getFormat()->get('duration'),
            'sample_rate' => $audioStream->get('sample_rate'),
            'channels' => $audioStream->get('channels'),
            'bit_rate' => $audioStream->get('bit_rate'),
        ];
    }
    
    /**
     * 验证输入文件
     */
    private function validateInputFile(string $videoPath): void
    {
        if (!file_exists($videoPath)) {
            throw new AudioExtractionException("视频文件不存在: {$videoPath}");
        }
        
        if (!is_readable($videoPath)) {
            throw new AudioExtractionException("视频文件不可读: {$videoPath}");
        }
        
        $fileSize = filesize($videoPath);
        if ($fileSize === 0) {
            throw new AudioExtractionException("视频文件为空: {$videoPath}");
        }
        
        // 检查文件格式
        $allowedExtensions = ['mp4', 'avi', 'mkv', 'flv', 'mov', 'wmv'];
        $extension = strtolower(pathinfo($videoPath, PATHINFO_EXTENSION));
        
        if (!in_array($extension, $allowedExtensions)) {
            throw new AudioExtractionException("不支持的视频格式: {$extension}");
        }
    }
}

/**
 * 音频提取选项
 */
class AudioExtractionOptions
{
    private string $format = 'wav';
    private int $sampleRate = 16000;  // 16kHz 适合语音识别
    private int $channels = 1;        // 单声道
    private int $bitrate = 128;       // kbps
    private ?float $startTime = null;
    private ?float $duration = null;
    
    public function setFormat(string $format): self
    {
        $this->format = $format;
        return $this;
    }
    
    public function setSampleRate(int $sampleRate): self
    {
        $this->sampleRate = $sampleRate;
        return $this;
    }
    
    public function setChannels(int $channels): self
    {
        $this->channels = $channels;
        return $this;
    }
    
    public function setBitrate(int $bitrate): self
    {
        $this->bitrate = $bitrate;
        return $this;
    }
    
    public function setTimeRange(float $startTime, float $duration): self
    {
        $this->startTime = $startTime;
        $this->duration = $duration;
        return $this;
    }
    
    public function getFormat(): string { return $this->format; }
    public function getSampleRate(): int { return $this->sampleRate; }
    public function getChannels(): int { return $this->channels; }
    public function getBitrate(): int { return $this->bitrate; }
    public function getStartTime(): ?float { return $this->startTime; }
    public function getDuration(): ?float { return $this->duration; }
    
    public function hasTimeRange(): bool
    {
        return $this->startTime !== null && $this->duration !== null;
    }
    
    public function toArray(): array
    {
        return [
            'format' => $this->format,
            'sample_rate' => $this->sampleRate,
            'channels' => $this->channels,
            'bitrate' => $this->bitrate,
            'start_time' => $this->startTime,
            'duration' => $this->duration,
        ];
    }
}

/**
 * 音频提取结果
 */
class AudioExtractionResult
{
    public function __construct(
        public readonly string $outputPath,
        public readonly float $duration,
        public readonly int $sampleRate,
        public readonly int $channels,
        public readonly int $fileSize,
        public readonly float $processingTime
    ) {}
    
    public function toArray(): array
    {
        return [
            'output_path' => $this->outputPath,
            'duration' => $this->duration,
            'sample_rate' => $this->sampleRate,
            'channels' => $this->channels,
            'file_size' => $this->fileSize,
            'processing_time' => $this->processingTime,
        ];
    }
}
