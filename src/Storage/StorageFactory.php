<?php

declare(strict_types=1);

namespace LiveStream\Storage;

use LiveStream\Config\RecordingOptions;
use LiveStream\Storage\Contracts\ObjectStorageInterface;
use LiveStream\Storage\Adapters\S3Storage;
use LiveStream\Storage\Adapters\OssStorage;
use LiveStream\Storage\Adapters\CosStorage;

final class StorageFactory
{
    public static function makeFromOptions(RecordingOptions $options): ?ObjectStorageInterface
    {
        $driver = strtolower((string) $options->getStorage('driver', ''));

        return match ($driver) {
            's3' => new S3Storage($options->getStorage()),
            'oss' => new OssStorage($options->getStorage()),
            'cos' => new CosStorage($options->getStorage()),
            default => null,
        };
    }
}
