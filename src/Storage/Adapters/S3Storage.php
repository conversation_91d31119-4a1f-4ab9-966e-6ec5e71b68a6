<?php

declare(strict_types=1);

namespace LiveStream\Storage\Adapters;

use LiveStream\Storage\Contracts\ObjectStorageInterface;
use LiveStream\Storage\ValueObjects\FileUploadRequest;
use LiveStream\Storage\ValueObjects\FileUploadResult;

final class S3Storage implements ObjectStorageInterface
{
    public function __construct(private readonly array $config) {}

    public function upload(FileUploadRequest $request): FileUploadResult
    {
        if (!class_exists('Aws\\S3\\S3Client')) {
            return new FileUploadResult(false, error: 'AWS SDK not installed');
        }

        $clientConfig = [
            'version' => 'latest',
            'region' => $this->config['region'] ?? 'us-east-1',
            'credentials' => [
                'key' => $this->config['accessKey'] ?? '',
                'secret' => $this->config['secretKey'] ?? '',
            ],
        ];

        if (!empty($this->config['endpoint'])) {
            $clientConfig['endpoint'] = $this->config['endpoint'];
        }

        if (!empty($this->config['usePathStyleEndpoint'])) {
            $clientConfig['use_path_style_endpoint'] = (bool) $this->config['usePathStyleEndpoint'];
        }

        $concurrency = (int)($this->config['concurrency'] ?? 5);
        $partSize = (int)($this->config['partSize'] ?? 5 * 1024 * 1024);
        $retries = (int)($this->config['retries'] ?? 3);

        $uploaderClass = 'Aws\\S3\\MultipartUploader';
        $exceptionClass = 'Aws\\Exception\\MultipartUploadException';
        $s3ClientClass = 'Aws\\S3\\S3Client';

        $client = new $s3ClientClass($clientConfig);

        $source = $request->localPath;
        $bucket = $request->bucket;
        $key = ltrim($request->remotePath, '/');

        $attempt = 0;
        start:
        $attempt++;

        try {
            $uploader = new $uploaderClass($client, $source, [
                'bucket' => $bucket,
                'key' => $key,
                'concurrency' => $concurrency,
                'part_size' => $partSize,
            ]);

            $result = $uploader->upload();

            $url = method_exists($client, 'getObjectUrl')
                ? $client->getObjectUrl($bucket, $key)
                : ($this->config['endpoint'] ?? '') . '/' . $bucket . '/' . $key;

            return new FileUploadResult(true, url: $url, etag: $result['ETag'] ?? null, provider: 's3');
        } catch (\Throwable $e) {
            if ($attempt <= $retries) {
                if (class_exists($exceptionClass) && $e instanceof $exceptionClass) {
                    $uploaderClass::abort($e->getState());
                }
                usleep(200000 * $attempt);
                goto start;
            }

            return new FileUploadResult(false, error: $e->getMessage(), provider: 's3');
        }
    }
}
