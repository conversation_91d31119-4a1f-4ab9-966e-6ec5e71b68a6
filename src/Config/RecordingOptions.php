<?php

declare(strict_types=1);

namespace LiveStream\Config;

use LiveStream\Enum\Quality;
use LiveStream\Enum\OutputFormat;
use LiveStream\Exceptions\RecordingException;


class RecordingOptions extends Config
{
    public function setQuality(Quality $quality): static
    {
        $this->set('quality', $quality);
        return $this;
    }
    
    public function getQuality(mixed $default = Quality::ORIGINAL): Quality
    {
        return $this->get('quality', $default);
    }
    
    
    public function setFormat(OutputFormat $format): static
    {
        $this->set('format', $format);
        return $this;
    }

    public function setSavePath(string $path): static
    {
        $this->set('savePath', $path);
        return $this;
    }
    
    public function getSavePath(mixed $default = __DIR__ . '/downloads'): string
    {
        return $this->get('savePath', $default);
    }
    
    public function getFormat(mixed $default = OutputFormat::MP4): OutputFormat
    {
        return $this->get('format', $default);
    }

    public function setFfmpegOptions(string $key, mixed $value = null): static
    {
        $this->set("ffmpegOptions.{$key}", $value);
        return $this;
    }

    public function getFfmpegOptions(?string $key = null, mixed $default = []): array
    {
        if ($key === null) {
            return $this->get("ffmpegOptions", $default);
        }

        return $this->get("ffmpegOptions.{$key}", $default);
    }
}
