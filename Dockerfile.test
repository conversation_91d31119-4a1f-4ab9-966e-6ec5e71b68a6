# 测试专用的 Dockerfile
FROM php:8.3-cli

# 安装系统依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    ffmpeg \
    git \
    unzip \
    libzip-dev \
    $PHPIZE_DEPS \
    && docker-php-ext-install zip pcntl \
    && docker-php-ext-enable pcntl \
    && apt-get purge -y --auto-remove -o APT::AutoRemove::RecommendsImportant=false $PHPIZE_DEPS \
    && rm -rf /var/lib/apt/lists/*

# 安装 Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# 设置工作目录
WORKDIR /app

# 复制项目文件
COPY . .

# 安装依赖
RUN composer install --no-interaction --prefer-dist

# 验证 FFmpeg 安装
RUN ffmpeg -version

# 验证 PHP 扩展加载情况（确保 pcntl 已启用）
RUN php -m | grep -qi pcntl

# 默认命令
CMD ["./vendor/bin/pest"]