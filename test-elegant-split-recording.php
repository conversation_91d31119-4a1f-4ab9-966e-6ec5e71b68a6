<?php

require_once __DIR__ . '/vendor/autoload.php';

use LiveStream\LiveStream;
use LiveStream\PlatformFactory;
use LiveStream\Recording\PendingRecorder;
use LiveStream\Config\RecordingOptions;
use LiveStream\Recording\Advanced\PhpFFmpegRecorder;
use LiveStream\Enum\Quality;
use LiveStream\Enum\OutputFormat;

echo "🎬 优雅的抖音直播分割录制测试\n";
echo "==============================\n\n";

try {
    // 1. 创建直播平台驱动
    $liveStream = new LiveStream(new PlatformFactory());
    $platform = $liveStream->driver('https://live.douyin.com/426219276305');

    echo "✅ 成功连接到直播间\n";

    // 2. 获取房间信息
    $roomInfo = $platform->getRoomInfo();
    echo "房间ID: " . $roomInfo->getRoomId() . "\n";
    echo "主播: " . $roomInfo->getAnchorName() . "\n";
    echo "标题: " . $roomInfo->getTitle() . "\n";
    echo "是否在线: " . ($roomInfo->isLive() ? '是' : '否') . "\n\n";

    if (!$roomInfo->isLive()) {
        echo "❌ 直播间当前未开播，无法录制\n";
        exit(1);
    }

    // 3. 创建优雅的分割录制选项
    $options = new RecordingOptions(
        quality: Quality::HIGH,
        format: OutputFormat::MP4,
        savePath: './elegant-recordings',
        splitTime: 20, // 每20秒分割一次
        timeoutSeconds: 80 // 总录制时间80秒，预计4个分段
    );

    // 4. 创建录制配置
    $pendingRecorder = new PendingRecorder($roomInfo, $options);

    echo "📋 优雅分割录制配置:\n";
    $recordingInfo = $pendingRecorder->getRecordingInfo();
    echo "  录制ID: " . $recordingInfo['record_id'] . "\n";
    echo "  画质: " . $recordingInfo['quality'] . "\n";
    echo "  格式: " . $recordingInfo['format'] . "\n";
    echo "  基础文件名: " . pathinfo($recordingInfo['output_path'], PATHINFO_FILENAME) . "\n";
    echo "  分割时间: {$options->splitTime} 秒\n";
    echo "  总录制时间: {$options->timeoutSeconds} 秒\n";
    echo "  预计分段数: " . ceil($options->timeoutSeconds / $options->splitTime) . " 个\n";
    echo "  流地址: " . substr($recordingInfo['stream_url'], 0, 80) . "...\n\n";

    // 5. 创建录制器
    $recorder = new PhpFFmpegRecorder();

    echo "🎬 开始优雅的分割录制...\n";
    echo "使用 php-ffmpeg 扩展包进行精确分割\n";
    echo "按 Ctrl+C 可提前停止录制\n\n";

    // 6. 执行分割录制（使用新的优雅架构）
    $startTime = time();
    $result = $recorder->execute($pendingRecorder, function ($media, $format, $percentage, $segment = null) {
        if ($segment) {
            echo "\r分段 {$segment->index} 录制进度: " . number_format($percentage, 1) . "%";
        } else {
            echo "\r录制进度: " . number_format($percentage, 1) . "%";
        }
    });

    $totalTime = time() - $startTime;

    echo "\n\n✅ 优雅分割录制完成！\n";
    echo "总录制时间: {$totalTime} 秒\n";

    // 7. 检查生成的文件
    $baseOutputPath = $result->getOutputPath();
    $pathInfo = pathinfo($baseOutputPath);
    $baseName = $pathInfo['dirname'] . '/' . $pathInfo['filename'];
    $extension = $pathInfo['extension'] ?? 'mp4';

    echo "\n📁 生成的文件:\n";
    $totalSize = 0;
    $fileCount = 0;

    for ($i = 1; $i <= 10; $i++) {
        $segmentPath = sprintf('%s_part%03d.%s', $baseName, $i, $extension);

        if (file_exists($segmentPath)) {
            $fileSize = filesize($segmentPath);
            $fileSizeMB = $fileSize / 1024 / 1024;
            $totalSize += $fileSize;
            $fileCount++;

            echo sprintf("  %d. %s (%.2f MB)\n", $i, basename($segmentPath), $fileSizeMB);

            // 获取视频时长
            $videoInfo = shell_exec("ffprobe -v quiet -print_format json -show_format " . escapeshellarg($segmentPath));
            if ($videoInfo) {
                $info = json_decode($videoInfo, true);
                if (isset($info['format']['duration'])) {
                    $duration = number_format($info['format']['duration'], 1);
                    echo "     时长: {$duration} 秒\n";
                }
            }
        }
    }

    if ($fileCount > 0) {
        $totalSizeMB = $totalSize / 1024 / 1024;
        echo "\n📊 统计信息:\n";
        echo "  文件总数: {$fileCount} 个\n";
        echo "  总大小: " . number_format($totalSizeMB, 2) . " MB\n";
        echo "  平均大小: " . number_format($totalSizeMB / $fileCount, 2) . " MB/文件\n";
        echo "  平均分段时长: " . number_format($options->splitTime, 1) . " 秒\n";

        echo "\n🏗️ 架构特点:\n";
        echo "  ✅ 使用 php-ffmpeg 扩展包而非命令行\n";
        echo "  ✅ 遵循 SOLID 原则和最佳实践\n";
        echo "  ✅ 单一职责：分离配置、执行、分割逻辑\n";
        echo "  ✅ 工厂模式：自动选择合适的分割器\n";
        echo "  ✅ 值对象：封装分段信息和状态\n";
        echo "  ✅ 集合类：优雅管理多个分段\n";
        echo "  ✅ 接口隔离：清晰的契约定义\n";
        echo "  ✅ 依赖注入：松耦合的设计\n";
    } else {
        echo "❌ 未找到录制文件\n";
    }
} catch (Exception $e) {
    echo "\n❌ 分割录制失败: " . $e->getMessage() . "\n";
    echo "错误类型: " . get_class($e) . "\n";

    if (method_exists($e, 'getContext')) {
        echo "错误上下文: " . json_encode($e->getContext(), JSON_PRETTY_PRINT) . "\n";
    }
}

echo "\n=== 优雅分割录制测试结束 ===\n";
