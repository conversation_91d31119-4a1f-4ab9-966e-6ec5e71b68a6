<?php

require_once __DIR__ . '/vendor/autoload.php';

use LiveStream\LiveStream;
use LiveStream\PlatformFactory;
use LiveStream\Recording\PendingRecorder;
use LiveStream\Config\RecordingOptions;
use LiveStream\Recording\Advanced\PhpFFmpegRecorder;
use LiveStream\Enum\Quality;
use LiveStream\Enum\OutputFormat;

echo "🚀 原生 FFmpeg 分割录制测试\n";
echo "=============================\n";
echo "基于 DouyinLiveRecorder 的成功实践\n";
echo "使用 FFmpeg 原生 -f segment 参数\n\n";

try {
    // 1. 创建直播平台驱动
    $liveStream = new LiveStream(new PlatformFactory());
    $platform = $liveStream->driver('https://live.douyin.com/426219276305');

    echo "✅ 成功连接到直播间\n";

    // 2. 获取房间信息
    $roomInfo = $platform->getRoomInfo();
    echo "房间ID: " . $roomInfo->getRoomId() . "\n";
    echo "主播: " . $roomInfo->getAnchorName() . "\n";
    echo "标题: " . $roomInfo->getTitle() . "\n";
    echo "是否在线: " . ($roomInfo->isLive() ? '是' : '否') . "\n\n";

    if (!$roomInfo->isLive()) {
        echo "❌ 直播间当前未开播，无法录制\n";
        exit(1);
    }

    // 3. 创建原生分割录制选项
    echo "🎯 原生 FFmpeg 分割的优势:\n";
    echo "• 使用 FFmpeg 原生 -f segment 参数\n";
    echo "• 真正的实时分割，无需后处理\n";
    echo "• 无额外内存和存储开销\n";
    echo "• 支持网络重连和容错处理\n";
    echo "• 与 DouyinLiveRecorder 相同的技术方案\n\n";

    $options = new RecordingOptions(
        quality: Quality::HIGH,
        format: OutputFormat::MP4,      // 原生分割支持 MP4
        savePath: './native-recordings',
        splitTime: 20,                  // 每20秒分割一次
        timeoutSeconds: 80              // 总录制80秒，预计4段
    );

    // 4. 创建录制配置
    $pendingRecorder = new PendingRecorder($roomInfo, $options);

    echo "📋 原生分割录制配置:\n";
    $recordingInfo = $pendingRecorder->getRecordingInfo();
    echo "  录制ID: " . $recordingInfo['record_id'] . "\n";
    echo "  分割方式: FFmpeg 原生 -f segment\n";
    echo "  分割时间: {$options->splitTime} 秒\n";
    echo "  总录制时间: {$options->timeoutSeconds} 秒\n";
    echo "  预计分段数: " . ceil($options->timeoutSeconds / $options->splitTime) . " 个\n";
    echo "  输出目录: " . dirname($recordingInfo['output_path']) . "\n";
    echo "  流地址: " . substr($recordingInfo['stream_url'], 0, 80) . "...\n\n";

    // 5. 创建录制器
    $recorder = new PhpFFmpegRecorder();

    echo "🚀 开始原生 FFmpeg 分割录制...\n";
    echo "📦 使用 FFmpeg 原生 -f segment 参数\n";
    echo "⚡ 真正的实时分割，无需等待录制完成\n";
    echo "🔄 自动网络重连和错误恢复\n";
    echo "💾 无额外内存和存储开销\n";
    echo "🛑 按 Ctrl+C 可提前停止录制\n\n";

    // 6. 执行原生分割录制
    $startTime = time();
    $result = $recorder->execute($pendingRecorder, function ($elapsed, $segmentCount) {
        // 这个回调会显示录制进度
        static $lastUpdate = 0;
        if (time() - $lastUpdate >= 3) {
            echo "\r⏱️  录制进度: {$elapsed}秒 | 分段数: {$segmentCount}";
            $lastUpdate = time();
        }
    });

    $totalTime = time() - $startTime;

    echo "\n\n✅ 原生分割录制完成！\n";
    echo "总录制时间: {$totalTime} 秒\n";

    // 7. 分析生成的文件
    echo "\n📊 分析生成的文件...\n";
    $recordingDir = './native-recordings';

    if (is_dir($recordingDir)) {
        $files = glob($recordingDir . '/*.mp4');
        sort($files, SORT_NATURAL);

        if (count($files) > 0) {
            $totalSize = 0;
            $totalDuration = 0;

            echo "生成的文件详情:\n";
            foreach ($files as $index => $file) {
                $fileSize = filesize($file);
                $fileSizeMB = $fileSize / 1024 / 1024;
                $totalSize += $fileSize;

                echo sprintf("  %d. %s\n", $index + 1, basename($file));
                echo sprintf("     大小: %.2f MB\n", $fileSizeMB);

                // 获取视频时长
                $videoInfo = shell_exec("ffprobe -v quiet -print_format json -show_format " . escapeshellarg($file));
                if ($videoInfo) {
                    $info = json_decode($videoInfo, true);
                    if (isset($info['format']['duration'])) {
                        $duration = (float)$info['format']['duration'];
                        $totalDuration += $duration;
                        echo sprintf("     时长: %.1f 秒\n", $duration);

                        // 计算比特率
                        $bitrate = ($fileSize * 8) / $duration / 1000; // kbps
                        echo sprintf("     比特率: %.0f kbps\n", $bitrate);

                        // 检查时长精度
                        $expectedDuration = $options->splitTime;
                        $durationDiff = abs($duration - $expectedDuration);
                        $accuracy = (1 - $durationDiff / $expectedDuration) * 100;
                        echo sprintf("     分割精度: %.1f%%\n", $accuracy);
                    }
                }
                echo "\n";
            }

            $totalSizeMB = $totalSize / 1024 / 1024;
            echo "📈 统计摘要:\n";
            echo "  文件总数: " . count($files) . " 个\n";
            echo "  总大小: " . number_format($totalSizeMB, 2) . " MB\n";
            echo "  总时长: " . number_format($totalDuration, 1) . " 秒\n";
            echo "  平均分段大小: " . number_format($totalSizeMB / count($files), 2) . " MB\n";
            echo "  平均分段时长: " . number_format($totalDuration / count($files), 1) . " 秒\n";
            echo "  平均比特率: " . number_format(($totalSize * 8) / $totalDuration / 1000, 0) . " kbps\n";

            // 验证分割精度
            $averageSegmentDuration = $totalDuration / count($files);
            $expectedDuration = $options->splitTime;
            $durationDiff = abs($averageSegmentDuration - $expectedDuration);
            $overallAccuracy = (1 - $durationDiff / $expectedDuration) * 100;

            echo "\n🎯 原生分割性能评估:\n";
            echo "  期望分段时长: {$expectedDuration} 秒\n";
            echo "  实际平均时长: " . number_format($averageSegmentDuration, 1) . " 秒\n";
            echo "  时长偏差: " . number_format($durationDiff, 1) . " 秒\n";
            echo "  整体精度: " . number_format($overallAccuracy, 1) . "%\n";

            if ($overallAccuracy >= 95) {
                echo "  ✅ 分割精度优秀！\n";
            } elseif ($overallAccuracy >= 90) {
                echo "  ✅ 分割精度良好！\n";
            } else {
                echo "  ⚠️  分割精度有待改善\n";
            }

            echo "\n🚀 原生 FFmpeg 分割的技术优势:\n";
            echo "  ✅ 使用 FFmpeg 原生 -f segment 参数\n";
            echo "  ✅ 真正的实时分割，无延迟\n";
            echo "  ✅ 无额外内存开销\n";
            echo "  ✅ 无需后处理步骤\n";
            echo "  ✅ 支持网络重连和容错\n";
            echo "  ✅ 与知名项目 DouyinLiveRecorder 相同方案\n";
            echo "  ✅ 生产环境验证的稳定性\n";

            // 与其他方案对比
            echo "\n📊 与其他分割方案对比:\n";
            echo "  原生分割 vs 智能分割:\n";
            echo "    • 内存使用: 原生更低\n";
            echo "    • 实时性: 原生更好\n";
            echo "    • 精度: 基本相同\n";
            echo "    • 稳定性: 原生更高\n";
            echo "    • 兼容性: 智能更广\n";
        } else {
            echo "❌ 未找到录制文件\n";
        }
    } else {
        echo "❌ 录制目录不存在\n";
    }
} catch (Exception $e) {
    echo "\n❌ 原生分割录制失败: " . $e->getMessage() . "\n";
    echo "错误类型: " . get_class($e) . "\n";

    if (method_exists($e, 'getContext')) {
        echo "错误上下文: " . json_encode($e->getContext(), JSON_PRETTY_PRINT) . "\n";
    }

    // 显示调试信息
    echo "\n🔧 调试信息:\n";
    echo "- 请确保 FFmpeg 二进制文件可用\n";
    echo "- 请检查网络连接和直播流状态\n";
    echo "- 原生分割器需要 FFmpeg 支持 -f segment 参数\n";
    echo "- 支持的格式：MP4、TS、MKV\n";
}

echo "\n=== 原生 FFmpeg 分割录制测试结束 ===\n";
